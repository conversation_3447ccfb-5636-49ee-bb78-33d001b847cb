import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import axios from 'axios';
import { getLauncherWindow } from '../../window';

interface ManifestFile {
  path: string;
  size: number;
  checksum: string;
  url: string;
}

interface DownloadProgress {
  file: string;
  totalFiles: number;
  currentFile: number;
  progress: number;
  totalProgress: number;
  speed: number;
  eta: number;
}

/**
 * Handles file downloading and checksum verification
 */
export class FileDownloader {
  private downloadPath: string;
  private abortController: AbortController | null = null;

  constructor(downloadPath: string) {
    this.downloadPath = downloadPath;
  }

  setAbortController(controller: AbortController | null): void {
    this.abortController = controller;
  }

  /**
   * Download a single file with progress reporting
   */
  async downloadFile(file: ManifestFile, fileIndex: number, totalFiles: number, customFilePath?: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[DOWNLOADER] Downloading ${file.path}`);
        
        // Use custom file path if provided, otherwise use proper directory structure
        const filePath = customFilePath || (() => {
          const properPath = file.path.replace(/^\.\//, '');
          return path.join(this.downloadPath, properPath);
        })();
        const fileDir = path.dirname(filePath);
        
        // Ensure directory exists
        await fs.ensureDir(fileDir);
        
        // Create write stream
        const writer = fs.createWriteStream(filePath);
        
        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;
        
        // Calculate timeout based on file size (minimum 60s, +30s per 50MB)
        const timeoutMs = Math.max(60000, 60000 + Math.floor(file.size / (50 * 1024 * 1024)) * 30000);
        console.log(`[DOWNLOADER] File ${file.path} (${(file.size / 1024 / 1024).toFixed(1)}MB) - timeout: ${timeoutMs / 1000}s`);

        // Download the file
        const response = await axios({
          method: 'get',
          url: file.url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: timeoutMs,
          headers: {
            'User-Agent': 'OneWhispr-Setup/1.0.0'
          }
        });
        
        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;
          
          // Calculate progress
          const progress = Math.min(100, Math.round((downloadedBytes / file.size) * 100));
          const totalProgress = Math.min(100, Math.round(
            ((fileIndex - 1) / totalFiles * 100) + (progress / totalFiles)
          ));
          
          // Calculate speed and ETA
          const now = Date.now();
          const timeDiff = (now - lastUpdateTime) / 1000; // in seconds
          
          if (timeDiff >= 0.5) { // Update every 500ms
            speed = Math.round((downloadedBytes - lastBytes) / timeDiff);
            lastBytes = downloadedBytes;
            lastUpdateTime = now;
          }
          
          // Calculate ETA
          const remainingBytes = file.size - downloadedBytes;
          const eta = speed > 0 ? Math.round(remainingBytes / speed) : 0;
          
          // Send progress update
          const launcherWindow = getLauncherWindow();
          if (launcherWindow) {
            launcherWindow.webContents.send('download:progress', {
              file: file.path,
              totalFiles,
              currentFile: fileIndex,
              progress,
              totalProgress,
              speed,
              eta
            } as DownloadProgress);
          }
        });
        
        // Handle completion
        response.data.pipe(writer);
        
        writer.on('finish', () => {
          console.log(`[DOWNLOADER] Successfully downloaded ${file.path}`);
          resolve();
        });
        writer.on('error', (error) => {
          console.error(`[DOWNLOADER] Write error for ${file.path}:`, error);
          reject(error);
        });

        response.data.on('error', (error: any) => {
          console.error(`[DOWNLOADER] Stream error for ${file.path}:`, error);
          reject(error);
        });
      } catch (error) {
        console.error(`[DOWNLOADER] Request error for ${file.path}:`, error);
        reject(error);
      }
    });
  }

  /**
   * Verify file checksum
   */
  async verifyChecksum(filePath: string, expectedChecksum: string): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const hash = crypto.createHash('sha256');
        const stream = fs.createReadStream(filePath);
        
        stream.on('data', (data) => {
          hash.update(data);
        });
        
        stream.on('end', () => {
          const fileChecksum = hash.digest('hex');
          const isValid = fileChecksum === expectedChecksum;
          
          if (!isValid) {
            console.error(`[DOWNLOADER] Checksum mismatch for ${filePath}`);
            console.error(`Expected: ${expectedChecksum}`);
            console.error(`Got: ${fileChecksum}`);
          }
          
          resolve(isValid);
        });
        
        stream.on('error', (error) => {
          console.error(`[DOWNLOADER] Error reading file for checksum: ${error}`);
          resolve(false);
        });
      } catch (error) {
        console.error('[DOWNLOADER] Error verifying checksum:', error);
        resolve(false);
      }
    });
  }
}
