/**
 * Service for checking and managing main app processes
 */
export class Process<PERSON>hecker {
  /**
   * Check if main app is already running by looking for the process
   */
  public async isMainAppAlreadyRunning(): Promise<boolean> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // Use tasklist to find "One Whispr.exe" processes
      const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq One Whispr.exe" /FO CSV');
      const lines = stdout.split('\n').filter(line => line.includes('One Whispr.exe'));

      let foundMainApp = false;
      let foundSetupApp = false;

      for (const line of lines) {
        // Parse CSV line to get PID (second column)
        const columns = line.split(',').map(col => col.replace(/"/g, '').trim());
        if (columns.length >= 2) {
          const pid = parseInt(columns[1]);
          
          if (pid === process.pid) {
            foundSetupApp = true;
            console.log('[PROCESS_CHECKER] Found setup app process (this process):', pid);
          } else {
            foundMainApp = true;
            console.log('[PROCESS_CHECKER] Found main app process:', pid);
          }
        }
      }

      if (foundMainApp) {
        console.log('[PROCESS_CHECKER] Main app is already running from the correct directory');
      } else if (foundSetupApp) {
        console.log('[PROCESS_CHECKER] Only setup app (this process) is running, main app is not running');
      } else {
        console.log('[PROCESS_CHECKER] No "One Whispr.exe" processes found');
      }

      return foundMainApp;
    } catch (error) {
      console.warn('[PROCESS_CHECKER] Error checking if main app process is running:', error);
      return false;
    }
  }

  /**
   * Get all running "One Whispr.exe" processes with details
   */
  public async getRunningProcesses(): Promise<Array<{ pid: number; isMainApp: boolean; isSetupApp: boolean }>> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq One Whispr.exe" /FO CSV');
      const lines = stdout.split('\n').filter(line => line.includes('One Whispr.exe'));

      const processes = [];

      for (const line of lines) {
        const columns = line.split(',').map(col => col.replace(/"/g, '').trim());
        if (columns.length >= 2) {
          const pid = parseInt(columns[1]);
          
          processes.push({
            pid,
            isMainApp: pid !== process.pid,
            isSetupApp: pid === process.pid
          });
        }
      }

      return processes;
    } catch (error) {
      console.warn('[PROCESS_CHECKER] Error getting running processes:', error);
      return [];
    }
  }
}
