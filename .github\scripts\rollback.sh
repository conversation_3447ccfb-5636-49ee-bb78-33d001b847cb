#!/bin/bash

# OneWhispr Rollback Script - VPS Version (Discord-Style Individual Files)
# This script gets deployed to the VPS automatically via GitHub Actions
# Usage: ./rollback.sh [version] or ./rollback.sh emergency

set -e

# This will be the correct path on the VPS after deployment
UPDATES_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)/public/updates"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "🔄 OneWhispr Rollback Tool (VPS Version - Individual Files)"
echo "======================================================"
echo "📍 Updates directory: $UPDATES_DIR"

# Check if we're in the right directory structure
if [ ! -d "$UPDATES_DIR/setup/versions" ] || [ ! -d "$UPDATES_DIR/main-app/versions" ]; then
    echo "❌ Updates directories not found:"
    echo "   Setup: $UPDATES_DIR/setup/versions"
    echo "   Main App: $UPDATES_DIR/main-app/versions"
    echo "💡 Make sure you're running this from the deployed scripts directory"
    exit 1
fi

cd "$UPDATES_DIR"

# Show available versions for both channels
echo ""
echo "📋 Available versions:"
echo "Setup Channel:"
ls setup/versions/ | sort -V | sed 's/^/  /' | nl
echo ""
echo "Main App Channel:"
ls main-app/versions/ | sort -V | sed 's/^/  /' | nl

# Determine target version
if [ "$1" = "emergency" ]; then
    TARGET_VERSION=$(ls setup/versions/ | sort -V | tail -2 | head -1)
    echo ""
    echo "🚨 EMERGENCY ROLLBACK to: $TARGET_VERSION"
elif [ -n "$1" ]; then
    TARGET_VERSION="$1"
    echo ""
    echo "🎯 ROLLING BACK to: $TARGET_VERSION"
else
    echo ""
    echo "Usage: $0 [version|emergency]"
    echo "Examples:"
    echo "  $0 1.2.1          # Rollback to specific version"
    echo "  $0 emergency      # Rollback to previous version"
    echo ""
    echo "Available versions (setup channel):"
    ls setup/versions/ | sort -V | sed 's/^/  /'
    exit 1
fi

# Verify target version exists in both channels
if [ ! -d "setup/versions/$TARGET_VERSION" ] || [ ! -d "main-app/versions/$TARGET_VERSION" ]; then
    echo "❌ ERROR: Version $TARGET_VERSION not found in one or both channels!"
    echo ""
    echo "Available setup versions:"
    ls setup/versions/ | sort -V | sed 's/^/  /'
    echo ""
    echo "Available main-app versions:"
    ls main-app/versions/ | sort -V | sed 's/^/  /'
    exit 1
fi

# Show current versions
CURRENT_SETUP_VERSION=$(cat setup/version.json 2>/dev/null | grep '"version"' | cut -d'"' -f4 || echo "unknown")
CURRENT_APP_VERSION=$(cat main-app/version.json 2>/dev/null | grep '"version"' | cut -d'"' -f4 || echo "unknown")

echo "📊 Current setup version: $CURRENT_SETUP_VERSION"
echo "📊 Current main app version: $CURRENT_APP_VERSION"
echo "🎯 Target version (both channels): $TARGET_VERSION"

# Confirm before proceeding
echo ""
read -p "⚠️  Are you sure you want to rollback BOTH channels to $TARGET_VERSION? [y/N]: " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Rollback cancelled"
    exit 1
fi

echo ""
echo "🔄 Starting dual-channel rollback process..."

# Backup current version.json files
if [ -f "setup/version.json" ]; then
    cp setup/version.json "setup/version.json.backup.$(date +%s)"
    echo "📦 Backed up current setup/version.json"
fi

if [ -f "main-app/version.json" ]; then
    cp main-app/version.json "main-app/version.json.backup.$(date +%s)"
    echo "📦 Backed up current main-app/version.json"
fi

# Update setup latest directory
echo "📁 Updating setup/latest directory..."
rm -rf setup/latest/*
cp "setup/versions/$TARGET_VERSION"/*.exe setup/latest/

# Update main-app latest directory
echo "📁 Updating main-app/latest directory..."
rm -rf main-app/latest/*
cp -r "main-app/versions/$TARGET_VERSION"/* main-app/latest/
# Update manifest for this version
if [ -f "manifest-$TARGET_VERSION.json" ]; then
  cp "manifest-$TARGET_VERSION.json" main-app/latest/manifest.json
fi

# Update Python components if they exist for this version
if [ -d "backend-runtime/versions/$TARGET_VERSION" ]; then
  echo "📁 Updating backend-runtime/latest directory..."
  rm -rf backend-runtime/latest/*
  cp -r "backend-runtime/versions/$TARGET_VERSION"/* backend-runtime/latest/
fi

if [ -d "backend-scripts/versions/$TARGET_VERSION" ]; then
  echo "📁 Updating backend-scripts/latest directory..."
  rm -rf backend-scripts/latest/*
  cp -r "backend-scripts/versions/$TARGET_VERSION"/* backend-scripts/latest/
fi

# Update setup version.json
echo "📝 Updating setup/version.json..."
cat > setup/version.json << EOF
{
  "version": "$TARGET_VERSION",
  "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "downloadUrl": "https://whispr.one/updates/setup/latest/Setup.exe",
  "releaseNotes": "Rolled back to version $TARGET_VERSION",
  "versionsUrl": "https://whispr.one/updates/setup/versions/$TARGET_VERSION/",
  "isRollback": true,
  "rolledBackFrom": "$CURRENT_SETUP_VERSION",
  "rolledBackAt": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

# Update main-app version.json
echo "📝 Updating main-app/version.json..."
cat > main-app/version.json << EOF
{
  "version": "$TARGET_VERSION",
  "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "releaseNotes": "Rolled back to version $TARGET_VERSION",
  "downloadUrl": "https://whispr.one/updates/main-app/latest/",
  "manifestUrl": "https://whispr.one/updates/main-app/latest/manifest.json",
  "versionsUrl": "https://whispr.one/updates/main-app/versions/$TARGET_VERSION/",
  "isRollback": true,
  "rolledBackFrom": "$CURRENT_APP_VERSION",
  "rolledBackAt": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

# Update backend runtime version.json if it exists
if [ -d "backend-runtime/latest" ]; then
  echo "📝 Updating backend-runtime/runtime-version.json..."
  cat > backend-runtime/runtime-version.json << EOF
{
  "version": "$TARGET_VERSION",
  "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "releaseNotes": "Rolled back backend runtime to version $TARGET_VERSION",
  "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
  "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$TARGET_VERSION/",
  "compressionType": "7z-lzma2-ultra",
  "isRollback": true,
  "rolledBackAt": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
fi

# Update backend scripts version.json if it exists
if [ -d "backend-scripts/latest" ]; then
  echo "📝 Updating backend-scripts/scripts-version.json..."
  cat > backend-scripts/scripts-version.json << EOF
{
  "version": "$TARGET_VERSION",
  "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "releaseNotes": "Rolled back backend scripts to version $TARGET_VERSION",
  "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
  "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$TARGET_VERSION/",
  "updateType": "bytecode",
  "compressionType": "7z",
  "isRollback": true,
  "rolledBackAt": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF
fi

echo ""
echo "✅ Successfully rolled back all components to version: $TARGET_VERSION"
echo ""
echo "🔍 New setup version info:"
echo "========================="
cat setup/version.json | head -15

echo ""
echo "🔍 New main-app version info:"
echo "============================"
cat main-app/version.json | head -15

echo ""
echo "🎉 File-based rollback complete!"
echo "📱 New users will now download setup version $TARGET_VERSION"
echo "🔄 Existing users will receive app rollback by downloading changed files on next app launch"
echo ""
echo "💡 To verify setup: curl https://whispr.one/updates/setup/version.json"
echo "💡 To verify main app: curl https://whispr.one/updates/main-app/version.json" 