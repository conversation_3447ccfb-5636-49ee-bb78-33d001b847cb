import { app, shell } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { getLauncherWindow, closeLauncherWindow } from '../../window';
import { MainAppDownloader } from '../download/mainAppDownloader';
import { BackendDownloader } from '../download/backendDownloader';
import { MicrosoftStoreHandler } from '../microsoft/storeHandler';
import { IS_MICROSOFT } from '../../constants';

/**
 * App Launcher Service - handles main app launching logic
 */
export class AppLauncher {
  private mainAppPath: string;
  private launchTimeout: NodeJS.Timeout | null = null;
  private isLaunching: boolean = false;
  private mainAppDownloader: MainAppDownloader;
  private backendDownloader: BackendDownloader;
  private microsoftStoreHandler: MicrosoftStoreHandler;

  constructor(
    mainAppDownloader: MainAppDownloader, 
    backendDownloader: BackendDownloader,
    microsoftStoreHandler: MicrosoftStoreHandler
  ) {
    this.mainAppDownloader = mainAppDownloader;
    this.backendDownloader = backendDownloader;
    this.microsoftStoreHandler = microsoftStoreHandler;

    // Set main app path based on environment
    const forceDownload = process.env.FORCE_DOWNLOAD === 'true';

    if (IS_MICROSOFT) {
      // Microsoft Store build - use AppData path (main app will be extracted there)
      this.mainAppPath = this.microsoftStoreHandler.getMainAppPath();
      console.log('[LAUNCHER] Microsoft Store build - using AppData path:', this.mainAppPath);
    } else if (!app.isPackaged && !forceDownload) {
      // In development mode without forced download, point directly to the built exe in win-unpacked
      this.mainAppPath = path.join(process.cwd(), '..', 'one.whispr-app', '.release', 'win-unpacked', 'One Whispr.exe');
      console.log('[LAUNCHER] Using development path:', this.mainAppPath);
    } else {
      // In production or forced download mode, use the downloaded files
      this.mainAppPath = path.join(this.mainAppDownloader.getDownloadPath(), 'One Whispr.exe');
      console.log('[LAUNCHER] Using downloaded files path:', this.mainAppPath);
    }

    // Main app process will be initialized when launching
    console.log('[LAUNCHER] Launcher initialized with path:', this.mainAppPath);
  }

  /**
   * Check if everything is ready to launch (main app + backend)
   */
  public async checkLaunchReady(): Promise<{
    mainAppReady: boolean,
    backendReady: boolean,
    allReady: boolean,
    mainAppNeeded: boolean,
    backendNeeded: boolean,
    reason: string
  }> {
    try {
      // Check if we're in development mode and should skip downloads
      const forceDownload = process.env.FORCE_DOWNLOAD === 'true';
      const isDev = !app.isPackaged && !forceDownload;

      console.log('[LAUNCHER] Environment check - isDev:', isDev, 'forceDownload:', forceDownload);
      console.log('[LAUNCHER] NODE_ENV:', process.env.NODE_ENV);

      // Check main app readiness based on build type
      let mainAppReady = false;
      let mainAppNeeded = false;
      let mainAppReason = '';

      if (IS_MICROSOFT) {
        // Microsoft Store build - check if first launch extraction is needed
        console.log('[LAUNCHER] Microsoft Store build detected');
        console.log('[LAUNCHER] Checking first launch status...');
        const isFirstLaunch = await this.microsoftStoreHandler.isFirstLaunch();
        console.log('[LAUNCHER] Is first launch:', isFirstLaunch);

        if (isFirstLaunch) {
          mainAppReady = false;
          mainAppNeeded = true;
          mainAppReason = 'Microsoft Store first launch - need to extract main app to AppData';
          console.log('[LAUNCHER] First launch detected - MainApp copy needed');
        } else {
          console.log('[LAUNCHER] Not first launch - checking if MainApp exists at:', this.mainAppPath);
          mainAppReady = fs.existsSync(this.mainAppPath);
          mainAppNeeded = false;
          mainAppReason = mainAppReady ? 'Microsoft Store - main app ready in AppData' : 'Microsoft Store - main app missing from AppData';
          console.log('[LAUNCHER] MainApp exists check result:', mainAppReady);
        }
      } else if (isDev && !forceDownload) {
        console.log('[LAUNCHER] Development mode detected - skipping main app download checks');
        mainAppReady = fs.existsSync(this.mainAppPath);
        mainAppNeeded = false; // Don't download in dev mode
        mainAppReason = mainAppReady ? 'Development mode - using local files' : 'Main app not built yet';
      } else {
        // Production mode - check if main app download is needed
        const mainAppCheck = await this.mainAppDownloader.checkDownloadNeeded();
        mainAppReady = !mainAppCheck.needed;
        mainAppNeeded = mainAppCheck.needed;
        mainAppReason = mainAppCheck.reason;
      }

      console.log('[LAUNCHER] Main app path:', this.mainAppPath);
      console.log('[LAUNCHER] Main app ready:', mainAppReady);
      console.log('[LAUNCHER] Main app check result:', { needed: mainAppNeeded, reason: mainAppReason });

      // Check backend
      const backendReady = await this.backendDownloader.isBackendReady();
      console.log('[LAUNCHER] Backend ready:', backendReady);

      const backendCheck = await this.backendDownloader.checkBackendUpdate();
      console.log('[LAUNCHER] Backend check result:', backendCheck);

      const allReady = mainAppReady && backendReady && !mainAppNeeded &&
                      !backendCheck.runtimeNeeded && !backendCheck.scriptsNeeded;

      let reason = 'Ready to launch';
      if (!mainAppReady || mainAppNeeded) {
        reason = mainAppReason || 'Main app needs download/update';
      } else if (!backendReady || backendCheck.runtimeNeeded || backendCheck.scriptsNeeded) {
        reason = backendCheck.reason;
      }

      const result = {
        mainAppReady,
        backendReady: backendReady,
        allReady,
        mainAppNeeded,
        backendNeeded: backendCheck.runtimeNeeded || backendCheck.scriptsNeeded,
        reason
      };

      console.log('[LAUNCHER] Final readiness result:', result);
      return result;
    } catch (error) {
      console.error('[LAUNCHER] Error checking launch readiness:', error);
      return {
        mainAppReady: false,
        backendReady: false,
        allReady: false,
        mainAppNeeded: true,
        backendNeeded: true,
        reason: 'Error checking readiness'
      };
    }
  }

  /**
   * Handle Microsoft Store download (copy from embedded resources)
   */
  public async handleMicrosoftStoreDownload(progressCallback?: (progress: number) => void): Promise<boolean> {
    if (!IS_MICROSOFT) {
      return false;
    }

    try {
      console.log('[LAUNCHER] Starting Microsoft Store MainApp copy (replaces download)...');

      // Copy MainApp from embedded resources to AppData (replaces download step)
      const success = await this.microsoftStoreHandler.copyMainAppToAppData(progressCallback);

      if (success) {
        console.log('[LAUNCHER] Microsoft Store MainApp copy completed successfully');
        return true;
      } else {
        console.error('[LAUNCHER] Microsoft Store MainApp copy failed');
        return false;
      }
    } catch (error) {
      console.error('[LAUNCHER] Error during Microsoft Store MainApp copy:', error);
      return false;
    }
  }

  /**
   * Legacy method for compatibility - now uses the integrated download flow
   */
  public async handleMicrosoftStoreSetup(): Promise<boolean> {
    return this.handleMicrosoftStoreDownload();
  }

  /**
   * Check if main app is already running by looking for the process
   */
  private async isMainAppAlreadyRunning(): Promise<boolean> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // Use tasklist to find "One Whispr.exe" processes
      const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq One Whispr.exe" /FO CSV');
      const lines = stdout.split('\n').filter(line => line.includes('One Whispr.exe'));

      let foundMainApp = false;
      let foundSetupApp = false;

      for (const line of lines) {
        // Parse CSV line to get PID (second column)
        const columns = line.split(',').map(col => col.replace(/"/g, '').trim());
        if (columns.length >= 2) {
          const pid = parseInt(columns[1]);

          if (pid === process.pid) {
            foundSetupApp = true;
            console.log('[LAUNCHER] Found setup app process (this process):', pid);
          } else {
            foundMainApp = true;
            console.log('[LAUNCHER] Found main app process:', pid);
          }
        }
      }

      if (foundMainApp) {
        console.log('[LAUNCHER] Main app is already running from the correct directory');
      } else if (foundSetupApp) {
        console.log('[LAUNCHER] Only setup app (this process) is running, main app is not running');
      } else {
        console.log('[LAUNCHER] No "One Whispr.exe" processes found');
      }

      return foundMainApp;
    } catch (error) {
      console.warn('[LAUNCHER] Error checking if main app process is running:', error);
      return false;
    }
  }

  /**
   * Launch the main app using Electron's shell API (like double-clicking)
   */
  public async launchMainApp(): Promise<boolean> {
    // Prevent double launches
    if (this.isLaunching) {
      console.log('[LAUNCHER] Launch already in progress, ignoring duplicate request');
      return true;
    }

    try {
      this.isLaunching = true;
      console.log('[LAUNCHER] Setting isLaunching = true');
      console.log('[LAUNCHER] Attempting to launch main app...');
      console.log('[LAUNCHER] Main app path:', this.mainAppPath);
      console.log('[LAUNCHER] Path exists:', fs.existsSync(this.mainAppPath));
      console.log('[LAUNCHER] Is Microsoft Store build:', IS_MICROSOFT);

      // Check if the main app exists
      if (!fs.existsSync(this.mainAppPath)) {
        console.error('[LAUNCHER] Main app not found:', this.mainAppPath);

        // Notify renderer
        const launcherWindow = getLauncherWindow();
        if (launcherWindow) {
          launcherWindow.webContents.send('launcher:main-app-error', {
            error: 'Main app not found',
            path: this.mainAppPath
          });
        }

        this.isLaunching = false;
        return false;
      }

      // Check if main app is already running
      if (await this.isMainAppAlreadyRunning()) {
        console.log('[LAUNCHER] Main app is already running, closing launcher immediately');
        this.isLaunching = false;

        // Close launcher immediately since main app is already open
        setTimeout(() => {
          closeLauncherWindow();
          setTimeout(() => {
            app.quit();
            setTimeout(() => process.exit(0), 500);
          }, 100);
        }, 500); // Small delay to show the message

        return true;
      }

      // Create shared state file for communication
      const sharedStatePath = path.join(app.getPath('temp'), 'whispr-launcher-state.json');
      const initialState = {
        launcherPid: process.pid,
        launcherReady: true,
        mlLibrariesReady: false,
        showSettings: false,
        timestamp: Date.now()
      };

      try {
        fs.writeFileSync(sharedStatePath, JSON.stringify(initialState, null, 2));
        console.log('[LAUNCHER] Shared state file created:', sharedStatePath);
      } catch (error) {
        console.error('[LAUNCHER] Failed to create shared state file:', error);
        throw error;
      }

      console.log('[LAUNCHER] About to call shell.openExternal with path:', this.mainAppPath);

      try {
        await shell.openExternal(this.mainAppPath);
        console.log('[LAUNCHER] shell.openExternal() completed successfully');

        // Give the process a moment to start, then check if it's running
        await new Promise(resolve => setTimeout(resolve, 2000));

        const isRunning = await this.isMainAppAlreadyRunning();
        console.log('[LAUNCHER] Main app running check after launch:', isRunning);

        if (!isRunning) {
          console.error('[LAUNCHER] WARNING: shell.openExternal() returned but main app process was not detected');
          console.log('[LAUNCHER] This might indicate the exe failed to start or crashed immediately');

          // Try alternative launch method with spawn as fallback
          console.log('[LAUNCHER] Attempting fallback launch with spawn...');
          const { spawn } = await import('child_process');
          const mainAppDir = path.dirname(this.mainAppPath);

          const mainAppProcess = spawn(this.mainAppPath, [], {
            cwd: mainAppDir,
            detached: true,
            stdio: 'ignore',
            windowsHide: false,
          });

          mainAppProcess.unref();
          console.log('[LAUNCHER] Fallback spawn completed with PID:', mainAppProcess.pid);
        }
      } catch (error) {
        console.error('[LAUNCHER] Error with shell.openExternal:', error);
        throw error;
      }

      console.log('[LAUNCHER] Main app launched, waiting for ML libraries initialization...');
      console.log('[LAUNCHER] Setting isLaunching = false after successful launch');
      this.isLaunching = false;

      // Wait for ML libraries ready signal via shared memory
      this.waitForMainAppReadyViaSharedMemory(sharedStatePath);

      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error launching main app:', error);
      this.isLaunching = false;

      // Notify renderer of error
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('launcher:main-app-error', {
          error: 'Failed to launch main app',
          details: error instanceof Error ? error.message : String(error)
        });
      }

      return false;
    }
  }

  /**
   * Wait for main app to signal readiness via shared memory
   */
  private waitForMainAppReadyViaSharedMemory(sharedStatePath: string): void {
    console.log('[LAUNCHER] Starting shared memory monitoring for main app readiness...');
    console.log('[LAUNCHER] Shared state path:', sharedStatePath);

    const checkInterval = 1000; // Check every 1 second
    let checkCount = 0;
    const maxChecks = 60; // Maximum 60 seconds

    const checkSharedState = () => {
      checkCount++;
      console.log(`[LAUNCHER] Checking shared state (attempt ${checkCount}/${maxChecks})...`);

      try {
        if (fs.existsSync(sharedStatePath)) {
          const stateContent = fs.readFileSync(sharedStatePath, 'utf8');
          const state = JSON.parse(stateContent);
          console.log('[LAUNCHER] Current shared state:', state);

          if (state.mlLibrariesReady) {
            console.log('[LAUNCHER] ML libraries are ready! Main app initialization complete.');
            this.closeLauncher();
            return;
          }
        } else {
          console.log('[LAUNCHER] Shared state file does not exist yet');
        }
      } catch (error) {
        console.error('[LAUNCHER] Error reading shared state:', error);
      }

      // Continue checking if we haven't reached the limit
      if (checkCount < maxChecks) {
        setTimeout(checkSharedState, checkInterval);
      } else {
        console.log('[LAUNCHER] Timeout waiting for main app readiness, closing anyway');
        this.closeLauncher();
      }
    };

    // Start checking after a short delay
    setTimeout(checkSharedState, checkInterval);

    // Set up timeout as fallback
    const maxWaitTime = 60000; // Maximum 60 seconds
    this.launchTimeout = setTimeout(() => {
      console.log('[LAUNCHER] Timeout waiting for main app readiness (60s), closing anyway');
      this.closeLauncher();
    }, maxWaitTime);
  }

  /**
   * Close the launcher properly
   */
  private closeLauncher(): void {
    console.log('[LAUNCHER] Closing launcher after main app is ready');
    console.log('[LAUNCHER] Resetting isLaunching flag in closeLauncher');
    this.isLaunching = false;

    // Clear any pending timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // No process cleanup needed since we use shell.openExternal

    // Close the launcher window properly (sets isQuitting flag)
    closeLauncherWindow();

    // Force quit if the window close doesn't trigger app quit
    setTimeout(() => {
      console.log('[LAUNCHER] Force quitting if still running');
      app.quit();

      // If app.quit() doesn't work (common in dev mode), force exit
      setTimeout(() => {
        console.log('[LAUNCHER] Final force exit');
        process.exit(0);
      }, 2000);
    }, 1000);
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    // Clear launch timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // Reset launching flag
    this.isLaunching = false;
  }
}
