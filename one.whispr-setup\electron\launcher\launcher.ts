import { app, shell } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { getLauncherWindow, closeLauncherWindow } from '../window';
import { downloader } from '../helpers/main-app/mainAppDownloader';
import { backendDownloader } from '../helpers/backend/backendDownloader';
import { setupLauncherIpc } from './ipc';
import { IS_MICROSOFT } from '../constants';
import { microsoftStoreHandler } from '../helpers/microsoft/microsoftStoreHandler';

/**
 * Simplified launcher that handles main app launching
 */
class Launcher {
  private mainAppPath: string;
  private launchTimeout: NodeJS.Timeout | null = null;
  private isLaunching: boolean = false; // Add guard to prevent double launches

  constructor() {
    // Set main app path based on environment
    const forceDownload = process.env.FORCE_DOWNLOAD === 'true';

    if (IS_MICROSOFT) {
      // Microsoft Store build - use AppData path (main app will be extracted there)
      this.mainAppPath = microsoftStoreHandler.instance.getMainAppPath();
      console.log('[LAUNCHER] Microsoft Store build - using AppData path:', this.mainAppPath);
    } else if (!app.isPackaged && !forceDownload) {
      // In development mode without forced download, point directly to the built exe in win-unpacked
      this.mainAppPath = path.join(process.cwd(), '..', 'one.whispr-app', '.release', 'win-unpacked', 'One Whispr.exe');
      console.log('[LAUNCHER] Using development path:', this.mainAppPath);
    } else {
      // In production or forced download mode, use the downloaded files
      this.mainAppPath = path.join(downloader.getDownloadPath(), 'One Whispr.exe');
      console.log('[LAUNCHER] Using downloaded files path:', this.mainAppPath);
    }

    // Main app process will be initialized when launching

    // Setup IPC handlers
    setupLauncherIpc(this);
  }


  /**
   * Check if everything is ready to launch (main app + backend)
   */
  public async checkLaunchReady(): Promise<{
    mainAppReady: boolean,
    backendReady: boolean,
    allReady: boolean,
    mainAppNeeded: boolean,
    backendNeeded: boolean,
    reason: string
  }> {
    try {
      // Check if we're in development mode and should skip downloads
      const forceDownload = process.env.FORCE_DOWNLOAD === 'true';
      const isDev = !app.isPackaged && !forceDownload;

      console.log('[LAUNCHER] Environment check - isDev:', isDev, 'forceDownload:', forceDownload);
      console.log('[LAUNCHER] NODE_ENV:', process.env.NODE_ENV);

      // Check main app readiness based on build type
      let mainAppReady = false;
      let mainAppNeeded = false;
      let mainAppReason = '';

      if (IS_MICROSOFT) {
        // Microsoft Store build - check if first launch extraction is needed
        console.log('[LAUNCHER] Microsoft Store build detected');
        console.log('[LAUNCHER] Checking first launch status...');
        const isFirstLaunch = await microsoftStoreHandler.instance.isFirstLaunch();
        console.log('[LAUNCHER] Is first launch:', isFirstLaunch);

        if (isFirstLaunch) {
          mainAppReady = false;
          mainAppNeeded = true;
          mainAppReason = 'Microsoft Store first launch - need to extract main app to AppData';
          console.log('[LAUNCHER] First launch detected - MainApp copy needed');
        } else {
          console.log('[LAUNCHER] Not first launch - checking if MainApp exists at:', this.mainAppPath);
          mainAppReady = fs.existsSync(this.mainAppPath);
          mainAppNeeded = false;
          mainAppReason = mainAppReady ? 'Microsoft Store - main app ready in AppData' : 'Microsoft Store - main app missing from AppData';
          console.log('[LAUNCHER] MainApp exists check result:', mainAppReady);
        }
      } else if (isDev && !forceDownload) {
        console.log('[LAUNCHER] Development mode detected - skipping main app download checks');
        mainAppReady = fs.existsSync(this.mainAppPath);
        mainAppNeeded = false; // Don't download in dev mode
        mainAppReason = mainAppReady ? 'Development mode - using local files' : 'Main app not built yet';
      } else {
        // Production mode - check if main app download is needed
        const mainAppCheck = await downloader.checkDownloadNeeded();
        mainAppReady = !mainAppCheck.needed;
        mainAppNeeded = mainAppCheck.needed;
        mainAppReason = mainAppCheck.reason;
      }

      console.log('[LAUNCHER] Main app path:', this.mainAppPath);
      console.log('[LAUNCHER] Main app ready:', mainAppReady);
      console.log('[LAUNCHER] Main app check result:', { needed: mainAppNeeded, reason: mainAppReason });

      // Check backend
      const backendReady = await backendDownloader.isBackendReady();
      console.log('[LAUNCHER] Backend ready:', backendReady);

      const backendCheck = await backendDownloader.checkBackendUpdate();
      console.log('[LAUNCHER] Backend check result:', backendCheck);

      const allReady = mainAppReady && backendReady && !mainAppNeeded &&
                      !backendCheck.runtimeNeeded && !backendCheck.scriptsNeeded;

      let reason = 'Ready to launch';
      if (!mainAppReady || mainAppNeeded) {
        reason = mainAppReason || 'Main app needs download/update';
      } else if (!backendReady || backendCheck.runtimeNeeded || backendCheck.scriptsNeeded) {
        reason = backendCheck.reason;
      }

      const result = {
        mainAppReady,
        backendReady: backendReady,
        allReady,
        mainAppNeeded,
        backendNeeded: backendCheck.runtimeNeeded || backendCheck.scriptsNeeded,
        reason
      };

      console.log('[LAUNCHER] Final readiness result:', result);
      return result;
    } catch (error) {
      console.error('[LAUNCHER] Error checking launch readiness:', error);
      return {
        mainAppReady: false,
        backendReady: false,
        allReady: false,
        mainAppNeeded: true,
        backendNeeded: true,
        reason: 'Error checking readiness'
      };
    }
  }

  /**
   * Handle Microsoft Store "download" - actually copies from embedded resources
   */
  public async handleMicrosoftStoreDownload(progressCallback?: (progress: number) => void): Promise<boolean> {
    if (!IS_MICROSOFT) {
      console.log('[LAUNCHER] Not a Microsoft Store build - skipping Microsoft Store download');
      return true;
    }

    try {
      console.log('[LAUNCHER] Starting Microsoft Store MainApp copy (replaces download)...');

      // Copy MainApp from embedded resources to AppData (replaces download step)
      const success = await microsoftStoreHandler.instance.copyMainAppToAppData(progressCallback);

      if (success) {
        console.log('[LAUNCHER] Microsoft Store MainApp copy completed successfully');
        return true;
      } else {
        console.error('[LAUNCHER] Microsoft Store MainApp copy failed');
        return false;
      }
    } catch (error) {
      console.error('[LAUNCHER] Error during Microsoft Store MainApp copy:', error);
      return false;
    }
  }

  /**
   * Legacy method for compatibility - now uses the integrated download flow
   */
  public async handleMicrosoftStoreSetup(): Promise<boolean> {
    return this.handleMicrosoftStoreDownload();
  }

  /**
   * Check if the main app is already running by checking processes
   */
  private async isMainAppAlreadyRunning(): Promise<boolean> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // Get detailed process list with command line to distinguish between setup and main app
      const { stdout } = await execAsync('wmic process where "name=\'One Whispr.exe\'" get ProcessId,CommandLine /format:csv');

      // Parse the CSV output to check if any process is running from the main app path
      const lines = stdout.split('\n').filter(line => line.trim());
      const mainAppDir = path.dirname(this.mainAppPath).toLowerCase();
      
      let foundMainApp = false;
      let foundSetupApp = false;
      
      for (const line of lines) {
        if (line.includes('One Whispr.exe') && line.includes(',')) {
          const parts = line.split(',');
          if (parts.length >= 2) {
            const commandLine = parts[1]?.toLowerCase() || '';
            
            // Check if this process is running from the main app directory
            if (commandLine.includes(mainAppDir)) {
              foundMainApp = true;
              console.log('[LAUNCHER] Found main app process running from:', commandLine);
            } else if (commandLine.includes('one whispr.exe')) {
              foundSetupApp = true;
              console.log('[LAUNCHER] Found setup app process (this process) running from:', commandLine);
            }
          }
        }
      }

      if (foundMainApp) {
        console.log('[LAUNCHER] Main app is already running from the correct directory');
      } else if (foundSetupApp) {
        console.log('[LAUNCHER] Only setup app (this process) is running, main app is not running');
      } else {
        console.log('[LAUNCHER] No "One Whispr.exe" processes found');
      }

      return foundMainApp;
    } catch (error) {
      console.warn('[LAUNCHER] Error checking if main app process is running:', error);
      return false;
    }
  }

  /**
   * Launch the main app using Electron's shell API (like double-clicking)
   */
  public async launchMainApp(): Promise<boolean> {
    // Prevent double launches
    if (this.isLaunching) {
      console.log('[LAUNCHER] Launch already in progress, ignoring duplicate request');
      return true;
    }

    try {
      this.isLaunching = true;
      console.log('[LAUNCHER] Setting isLaunching = true');
      console.log('[LAUNCHER] Attempting to launch main app...');
      console.log('[LAUNCHER] Main app path:', this.mainAppPath);
      console.log('[LAUNCHER] Path exists:', fs.existsSync(this.mainAppPath));
      console.log('[LAUNCHER] Is Microsoft Store build:', IS_MICROSOFT);
      
      // Check if the main app exists
      if (!fs.existsSync(this.mainAppPath)) {
        console.error('[LAUNCHER] Main app not found:', this.mainAppPath);
        
        // Notify renderer
        const launcherWindow = getLauncherWindow();
        if (launcherWindow) {
          launcherWindow.webContents.send('launcher:main-app-error', {
            error: 'Main app not found',
            path: this.mainAppPath
          });
        }
        
        this.isLaunching = false;
        return false;
      }
      
      // Check if main app is already running
      if (await this.isMainAppAlreadyRunning()) {
        console.log('[LAUNCHER] Main app is already running, closing launcher immediately');
        this.isLaunching = false;

        // Close launcher immediately since main app is already open
        setTimeout(() => {
          closeLauncherWindow();
          setTimeout(() => {
            app.quit();
            setTimeout(() => process.exit(0), 500);
          }, 100);
        }, 500); // Small delay to show the message

        return true;
      }

      // Create shared state file for communication
      const sharedStatePath = path.join(app.getPath('temp'), 'whispr-launcher-state.json');
      const initialState = {
        launcherPid: process.pid,
        launcherReady: true,
        mlLibrariesReady: false,
        showSettings: false,
        timestamp: Date.now()
      };

      try {
        fs.writeFileSync(sharedStatePath, JSON.stringify(initialState, null, 2));
        console.log('[LAUNCHER] Shared state file created:', sharedStatePath);
      } catch (error) {
        console.error('[LAUNCHER] Failed to create shared state file:', error);
        throw error;
      }

      console.log('[LAUNCHER] About to call shell.openExternal with path:', this.mainAppPath);
      
      try {
        await shell.openExternal(this.mainAppPath);
        console.log('[LAUNCHER] shell.openExternal() completed successfully');
        
        // Give the process a moment to start, then check if it's running
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const isRunning = await this.isMainAppAlreadyRunning();
        console.log('[LAUNCHER] Main app running check after launch:', isRunning);
        
        if (!isRunning) {
          console.error('[LAUNCHER] WARNING: shell.openExternal() returned but main app process was not detected');
          console.log('[LAUNCHER] This might indicate the exe failed to start or crashed immediately');
          
          // Try alternative launch method with spawn as fallback
          console.log('[LAUNCHER] Attempting fallback launch with spawn...');
          const { spawn } = await import('child_process');
          const mainAppDir = path.dirname(this.mainAppPath);
          
          const mainAppProcess = spawn(this.mainAppPath, [], {
            cwd: mainAppDir,
            detached: true,
            stdio: 'ignore',
            windowsHide: false,
          });
          
          mainAppProcess.unref();
          console.log('[LAUNCHER] Fallback spawn completed with PID:', mainAppProcess.pid);
        }
      } catch (error) {
        console.error('[LAUNCHER] shell.openExternal() failed:', error);
        throw error;
      }

      console.log('[LAUNCHER] Main app launched, waiting for ML libraries initialization...');
      console.log('[LAUNCHER] Setting isLaunching = false after successful launch');
      this.isLaunching = false;

      // Wait for ML libraries ready signal via shared memory
      this.waitForMainAppReadyViaSharedMemory(sharedStatePath);
      
      return true;
    } catch (error) {
      console.error('[LAUNCHER] Error launching main app:', error);
      this.isLaunching = false;
      
      // Notify renderer of error
      const launcherWindow = getLauncherWindow();
      if (launcherWindow) {
        launcherWindow.webContents.send('launcher:main-app-error', {
          error: 'Failed to launch main app',
          details: error instanceof Error ? error.message : String(error)
        });
      }
      
      return false;
    }
  }
  
  /**
   * Wait for main app to be ready (ML libraries initialized) via shared memory
   */
  private waitForMainAppReadyViaSharedMemory(sharedStatePath: string): void {
    console.log('[LAUNCHER] Waiting for ML libraries initialization via shared memory...');

    // Watch for changes to the shared state file
    fs.watchFile(sharedStatePath, { interval: 200 }, (curr, prev) => {
      // Check if file was modified
      if (curr.mtime > prev.mtime) {
        try {
          const stateData = fs.readFileSync(sharedStatePath, 'utf8');
          const state = JSON.parse(stateData);

          console.log('[LAUNCHER] Shared state updated:', state);

          // Check if ML libraries are ready
          if (state.mlLibrariesReady) {
            console.log('[LAUNCHER] Received ML libraries ready signal from main app');

            // Stop watching the file
            fs.unwatchFile(sharedStatePath);

            // Update state to signal that we want to show settings
            state.showSettings = true;
            state.launcherClosing = true;
            state.timestamp = Date.now();

            try {
              fs.writeFileSync(sharedStatePath, JSON.stringify(state, null, 2));
              console.log('[LAUNCHER] Updated shared state to show settings');
            } catch (error) {
              console.error('[LAUNCHER] Failed to update shared state:', error);
            }

            // Close launcher window and quit
            console.log('[LAUNCHER] Closing launcher window');
            closeLauncherWindow();

            setTimeout(() => {
              app.quit();
              setTimeout(() => process.exit(0), 500);
            }, 100);
          }
        } catch (error) {
          console.error('[LAUNCHER] Error reading shared state:', error);
        }
      }
    });

    // Set a timeout in case the main app doesn't respond
    setTimeout(() => {
      console.log('[LAUNCHER] Timeout waiting for ML libraries, checking if main app is running...');
      console.log('[LAUNCHER] Resetting isLaunching flag due to timeout');
      this.isLaunching = false;

      // Stop watching
      fs.unwatchFile(sharedStatePath);

      // Clean up shared state file
      try {
        if (fs.existsSync(sharedStatePath)) {
          fs.unlinkSync(sharedStatePath);
        }
      } catch (error) {
        // Ignore cleanup errors
      }

      // Close launcher anyway
      this.closeLauncher();
    }, 30000); // 30 second timeout

    // Set up timeout as fallback
    const maxWaitTime = 60000; // Maximum 60 seconds
    this.launchTimeout = setTimeout(() => {
      console.log('[LAUNCHER] Timeout waiting for main app readiness (60s), closing anyway');
      this.closeLauncher();
    }, maxWaitTime);
  }

  /**
   * Close the launcher properly
   */
  private closeLauncher(): void {
    console.log('[LAUNCHER] Closing launcher after main app is ready');
    console.log('[LAUNCHER] Resetting isLaunching flag in closeLauncher');
    this.isLaunching = false;

    // Clear any pending timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // No process cleanup needed since we use shell.openExternal

    // Close the launcher window properly (sets isQuitting flag)
    closeLauncherWindow();

    // Force quit if the window close doesn't trigger app quit
    setTimeout(() => {
      console.log('[LAUNCHER] Force quitting if still running');
      app.quit();

      // If app.quit() doesn't work (common in dev mode), force exit
      setTimeout(() => {
        console.log('[LAUNCHER] Final force exit');
        process.exit(0);
      }, 2000);
    }, 1000);
  }

  /**
   * Re-initialize the launcher (useful for hot reload)
   */
  public reinitialize(): void {
    console.log('[LAUNCHER] Re-initializing launcher...');
    setupLauncherIpc(this);
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    // Clear launch timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // Reset launching flag
    this.isLaunching = false;

    // IPC handlers are cleaned up by the LauncherIpc class
  }
}

// Export singleton instance
export const launcher = new Launcher();
