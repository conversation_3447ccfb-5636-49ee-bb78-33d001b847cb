import { useEffect } from 'react';
import { DownloadProgress, BackendDownloadProgress, SetupState } from '@src/types/setup';

interface UseSetupEventsProps {
  setState: React.Dispatch<React.SetStateAction<SetupState>>;
  launchMainApp: () => Promise<boolean>;
}

/**
 * Hook for handling IPC events from the main process
 */
export const useSetupEvents = ({ setState, launchMainApp }: UseSetupEventsProps) => {
  useEffect(() => {
    if (!window.electron) {
      console.error('Electron API not available');
      return;
    }
    
    // Main app download progress
    const removeDownloadProgressListener = window.electron.ipcRenderer.on(
      'download:progress',
      (data: DownloadProgress) => {
        setState(prev => ({
          ...prev,
          downloadProgress: data,
          isDownloading: true
        }));
      }
    );

    // Main app download complete
    const removeDownloadCompleteListener = window.electron.ipcRenderer.on(
      'download:complete',
      (_data: any) => {
        setState(prev => ({
          ...prev,
          downloadComplete: true,
          isDownloading: false
        }));
      }
    );

    // Main app download error
    const removeDownloadErrorListener = window.electron.ipcRenderer.on(
      'download:error',
      (data: any) => {
        setState(prev => ({
          ...prev,
          downloadError: data.message || 'Unknown download error',
          isDownloading: false,
          currentPhase: 'error'
        }));
      }
    );

    // Backend download progress
    const removeBackendProgressListener = window.electron.ipcRenderer.on(
      'backend:progress',
      (data: BackendDownloadProgress) => {
        console.log('[FRONTEND] Received backend progress:', data);
        setState(prev => ({
          ...prev,
          backendProgress: data,
          backendDownloading: true,
          currentPhase: 'downloading' // Ensure we're in downloading phase
        }));
      }
    );

    // Backend download complete
    const removeBackendCompleteListener = window.electron.ipcRenderer.on(
      'backend:complete',
      async () => {
        console.log('[SETUP] Backend complete event received');
        setState(prev => ({
          ...prev,
          backendDownloading: false,
          backendComplete: true,
          backendProgress: null,
          backendError: null, // Clear any previous backend errors
          currentPhase: 'starting' // Move to starting phase when backend is ready
        }));

        // Launch main app immediately when backend is complete
        console.log('[SETUP] Backend complete - launching main app');
        await launchMainApp();
      }
    );

    // Backend download error
    const removeBackendErrorListener = window.electron.ipcRenderer.on(
      'backend:error',
      (data: { message: string }) => {
        setState(prev => ({
          ...prev,
          backendDownloading: false,
          backendError: data.message,
          backendProgress: null
        }));
      }
    );
    
    // Main app ready
    const removeMainAppReadyListener = window.electron.ipcRenderer.on(
      'launcher:main-app-ready',
      (_data: any) => {
        setState(prev => ({
          ...prev,
          mainAppReady: true,
          currentPhase: 'starting'
        }));
      }
    );
    
    // Main app error
    const removeMainAppErrorListener = window.electron.ipcRenderer.on(
      'launcher:main-app-error',
      (data: any) => {
        setState(prev => ({
          ...prev,
          mainAppError: data.error || 'Unknown main app error',
          currentPhase: 'error'
        }));
      }
    );
    
    // Clean up listeners
    return () => {
      removeDownloadProgressListener();
      removeDownloadCompleteListener();
      removeDownloadErrorListener();
      removeBackendProgressListener();
      removeBackendCompleteListener();
      removeBackendErrorListener();
      removeMainAppReadyListener();
      removeMainAppErrorListener();
    };
  }, [setState, launchMainApp]);
};
