import * as fs from 'fs-extra';
import * as path from 'path';
import { tmpdir } from 'os';
import sevenBin from '7zip-bin';
import { spawn } from 'child_process';
import { getLauncherWindow } from '../../window';
import { IS_MICROSOFT } from '../../constants';

interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

/**
 * Handles 7z archive extraction for backend components
 */
export class ArchiveExtractor {
  private backendPath: string;
  private tempSevenZipPath: string | null = null; // Track temp binary for cleanup

  constructor(backendPath: string) {
    this.backendPath = backendPath;
  }

  /**
   * Extract 7z archive using 7zip-bin
   */
  async extract7z(archivePath: string, extractPath: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[BACKEND] Extracting ${archivePath} to ${extractPath}`);

        // Verify the archive file exists and is readable
        if (!fs.existsSync(archivePath)) {
          throw new Error(`Archive file does not exist: ${archivePath}`);
        }

        const archiveStats = await fs.stat(archivePath);
        if (archiveStats.size === 0) {
          throw new Error(`Archive file is empty: ${archivePath}`);
        }

        console.log(`[BACKEND] Archive size: ${Math.round(archiveStats.size / 1024 / 1024)}MB`);

        // Ensure extraction directory exists and is writable
        await fs.ensureDir(extractPath);

        // Test if extraction directory is writable
        const testFile = path.join(extractPath, 'test-write.tmp');
        try {
          await fs.writeFile(testFile, 'test');
          await fs.remove(testFile);
        } catch (writeError) {
          throw new Error(`Extraction directory is not writable: ${extractPath}`);
        }

        // Get 7zip binary path - handle Microsoft Store builds differently
        let sevenZipPath: string;

        if (IS_MICROSOFT) {
          // For Microsoft Store builds, try to extract 7zip binary to a writable location
          const tempDir = path.join(tmpdir(), 'onewhispr-7zip');
          const tempBinaryPath = path.join(tempDir, '7za.exe');

          try {
            await fs.ensureDir(tempDir);

            // Copy 7zip binary from asar to temp directory (only if not already copied)
            const originalSevenZipPath = sevenBin.path7za;
            if (await fs.pathExists(originalSevenZipPath)) {
              if (!await fs.pathExists(tempBinaryPath)) {
                console.log(`[BACKEND] Microsoft Store: Copying 7zip binary to temp: ${tempBinaryPath}`);
                await fs.copy(originalSevenZipPath, tempBinaryPath);
              } else {
                console.log(`[BACKEND] Microsoft Store: Using existing temp 7zip binary: ${tempBinaryPath}`);
              }
              sevenZipPath = tempBinaryPath;
              this.tempSevenZipPath = tempBinaryPath; // Track for later cleanup
              console.log(`[BACKEND] Microsoft Store: Using 7zip binary: ${sevenZipPath}`);
            } else {
              throw new Error('7zip binary not found in asar');
            }
          } catch (error) {
            console.error('[BACKEND] Failed to copy 7zip binary for Microsoft Store:', error);
            // Fallback to original path
            sevenZipPath = sevenBin.path7za;
            this.tempSevenZipPath = null; // No temp file to clean up
          }
        } else {
          sevenZipPath = sevenBin.path7za;
        }

        console.log(`[BACKEND] Using 7zip binary: ${sevenZipPath}`);

        const extractType = archivePath.includes('Runtime') ? 'runtime' : 'scripts';

        // Arguments for 7zip extraction
        const args = [
          'x',                    // extract command
          archivePath,           // source file
          `-o${extractPath}`,    // output directory (no space after -o)
          '-y',                  // yes to all prompts
          '-aoa'                 // overwrite all files without prompting
        ];

        console.log(`[BACKEND] Running command: ${sevenZipPath} ${args.join(' ')}`);

        let stdout = '';
        let stderr = '';

        // Realistic progress simulation based on ~53 second extraction time
        let simulatedProgress = 0;
        const progressInterval = setInterval(() => {
          // Slower progress that more accurately reflects the 53-second extraction
          if (simulatedProgress < 30) {
            simulatedProgress += 3; // 0-30% in first 20 seconds (slower start)
          } else if (simulatedProgress < 70) {
            simulatedProgress += 2; // 30-70% in next 20 seconds (steady middle)
          } else if (simulatedProgress < 90) {
            simulatedProgress += 1; // 70-90% in next 10 seconds (slower end)
          }
          // Stay at 90% for the final few seconds until real completion

          if (simulatedProgress <= 90) {
            this.sendProgress(extractType, simulatedProgress, 0, 0, `Extracting ${extractType}...`);
          }
        }, 2000); // Update every 2 seconds

        const process = spawn(sevenZipPath, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          windowsHide: true
        });

        // Parse progress from stdout if available
        process.stdout?.on('data', (data) => {
          const output = data.toString();
          stdout += output;

          // Look for percentage patterns in 7zip output
          const progressMatch = output.match(/(\d+)%/);
          if (progressMatch) {
            const progress = parseInt(progressMatch[1]);
            console.log(`[BACKEND] Found progress: ${progress}%`);
            if (progress >= 0 && progress <= 100) {
              this.sendProgress(extractType, progress, 0, 0, `Extracting ${extractType}...`);
            }
          }
        });

        process.stderr?.on('data', (data) => {
          stderr += data.toString();
        });

        process.on('close', (code) => {
          clearInterval(progressInterval);

          console.log(`[BACKEND] 7zip process exited with code: ${code}`);
          if (stdout) console.log(`[BACKEND] 7zip stdout: ${stdout}`);
          if (stderr) console.log(`[BACKEND] 7zip stderr: ${stderr}`);

          if (code === 0) {
            console.log('[BACKEND] Extraction completed successfully');
            this.sendProgress(extractType, 100, 0, 0, `${extractType === 'runtime' ? 'Runtime' : 'Scripts'} extraction complete`);
            resolve();
          } else {
            let errorMessage = `7zip extraction failed with code ${code}`;
            if (stderr) errorMessage += `\nstderr: ${stderr}`;
            if (stdout) errorMessage += `\nstdout: ${stdout}`;

            // Common 7zip error codes
            switch (code) {
              case 1:
                errorMessage += '\n(Warning: Non-fatal errors occurred)';
                console.warn('[BACKEND] 7zip warning, but continuing...');
                this.sendProgress(extractType, 100, 0, 0, `${extractType === 'runtime' ? 'Runtime' : 'Scripts'} extraction complete`);
                resolve();
                return;
              case 2:
                errorMessage += '\n(Fatal error: Archive corrupted or not found)';
                break;
              case 7:
                errorMessage += '\n(Command line error)';
                break;
              case 8:
                errorMessage += '\n(Not enough memory)';
                break;
              case 255:
                errorMessage += '\n(User stopped the process)';
                break;
            }

            reject(new Error(errorMessage));
          }
        });

        process.on('error', (error) => {
          clearInterval(progressInterval);
          reject(new Error(`7zip process error: ${error.message}`));
        });

        // Set a timeout for very large files (30 minutes)
        setTimeout(() => {
          if (!process.killed) {
            clearInterval(progressInterval);
            process.kill();
            reject(new Error('7zip extraction timed out after 30 minutes'));
          }
        }, 30 * 60 * 1000);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Clean up temporary 7zip binary (call this after ALL extractions are complete)
   */
  async cleanupTempBinary(): Promise<void> {
    if (this.tempSevenZipPath) {
      try {
        console.log(`[BACKEND] Cleaning up temp 7zip binary: ${this.tempSevenZipPath}`);
        await fs.remove(this.tempSevenZipPath);
        
        // Also try to remove the temp directory if it's empty
        const tempDir = path.dirname(this.tempSevenZipPath);
        try {
          await fs.rmdir(tempDir);
          console.log(`[BACKEND] Cleaned up temp directory: ${tempDir}`);
        } catch (dirError) {
          // Directory might not be empty or have permissions issues, that's okay
          console.log(`[BACKEND] Could not remove temp directory (this is normal): ${dirError}`);
        }
        
        this.tempSevenZipPath = null;
      } catch (cleanupError) {
        console.warn(`[BACKEND] Warning: Could not clean up temp 7zip binary: ${cleanupError}`);
        // Don't throw - this is not critical
      }
    }
  }

  /**
   * Clean up info files from archives if version files exist, or rename info files to version files
   */
  async cleanupInfoFiles(): Promise<void> {
    try {
      // Handle runtime files
      const runtimeVersionPath = path.join(this.backendPath, 'runtime-version.json');
      const runtimeInfoPath = path.join(this.backendPath, 'runtime-info.json');

      if (fs.existsSync(runtimeVersionPath) && fs.existsSync(runtimeInfoPath)) {
        // Version file exists, remove info file
        console.log('[BACKEND] Cleaning up runtime-info.json (runtime-version.json exists)');
        await fs.remove(runtimeInfoPath);
      } else if (!fs.existsSync(runtimeVersionPath) && fs.existsSync(runtimeInfoPath)) {
        // No version file, rename info file to version file (Microsoft Store scenario)
        console.log('[BACKEND] Renaming runtime-info.json to runtime-version.json (Microsoft Store build)');
        await fs.move(runtimeInfoPath, runtimeVersionPath);
      }

      // Handle scripts files
      const scriptsVersionPath = path.join(this.backendPath, 'scripts', 'scripts-version.json');
      const scriptsInfoPath = path.join(this.backendPath, 'scripts', 'scripts-info.json');

      if (fs.existsSync(scriptsVersionPath) && fs.existsSync(scriptsInfoPath)) {
        // Version file exists, remove info file
        console.log('[BACKEND] Cleaning up scripts-info.json (scripts-version.json exists)');
        await fs.remove(scriptsInfoPath);
      } else if (!fs.existsSync(scriptsVersionPath) && fs.existsSync(scriptsInfoPath)) {
        // No version file, rename info file to version file (Microsoft Store scenario)
        console.log('[BACKEND] Renaming scripts-info.json to scripts-version.json (Microsoft Store build)');
        await fs.move(scriptsInfoPath, scriptsVersionPath);
      }
    } catch (error) {
      console.warn('[BACKEND] Error cleaning up info files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Send progress update to renderer
   */
  private sendProgress(type: 'runtime' | 'scripts', progress: number, speed: number, eta: number, status: string): void {
    const launcherWindow = getLauncherWindow();
    if (launcherWindow) {
      launcherWindow.webContents.send('backend:progress', {
        type,
        progress,
        speed,
        eta,
        status
      } as BackendDownloadProgress);
    }
  }
}
