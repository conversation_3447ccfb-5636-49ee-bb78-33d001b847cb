/**
 * Singleton instances for services that need to be shared across the application
 * This maintains backward compatibility while keeping the services clean
 */

import { MainAppDownloader } from './download/mainAppDownloader';
import { BackendDownloader } from './download/backendDownloader';
import { MicrosoftStoreHandler } from './microsoft/storeHandler';

// Create singleton instances
export const downloader = new MainAppDownloader();
export const backendDownloader = new BackendDownloader();
export const microsoftStoreHandler = new MicrosoftStoreHandler();
