import { app } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { BACKEND_UPDATES, IS_MICROSOFT } from '../../constants';
import { microsoftStoreHandler } from '../microsoft/microsoftStoreHandler';

interface BackendVersionInfo {
  version: string;
  releaseDate: string;
  releaseNotes: string;
  downloadUrl: string;
  compressionType?: string;
  updateType?: string;
  isRollback?: boolean;
  checksum?: string;
}

/**
 * Handles version checking for backend components
 */
export class VersionManager {
  private backendPath: string;

  constructor(backendPath: string) {
    this.backendPath = backendPath;
  }

  /**
   * Fetch version information for runtime or scripts
   */
  async fetchVersionInfo(type: 'runtime' | 'scripts'): Promise<BackendVersionInfo | null> {
    try {
      const url = BACKEND_UPDATES[type].versionUrl;
      console.log(`[BACKEND] Fetching ${type} version from ${url}`);

      const response = await axios.get(url, {
        timeout: 30000,
        headers: { 'User-Agent': 'OneWhispr-Setup/1.0.0' }
      });

      return response.data as BackendVersionInfo;
    } catch (error) {
      console.error(`[BACKEND] Error fetching ${type} version:`, error);
      return null;
    }
  }

  /**
   * Check if runtime update is needed
   */
  async isRuntimeUpdateNeeded(runtimeInfo: BackendVersionInfo | null): Promise<boolean> {
    if (!runtimeInfo) {
      return true; // First install
    }

    const exePath = path.join(this.backendPath, 'One Whispr Backend.exe');
    if (!fs.existsSync(exePath)) {
      return true; // Backend executable missing
    }

    const versionPath = path.join(this.backendPath, 'runtime-version.json');
    if (!fs.existsSync(versionPath)) {
      return true;
    }
    
    try {
      const currentVersion = await fs.readJson(versionPath);

      // For rollbacks, be extra careful with runtime (1.2GB download)
      if (runtimeInfo.isRollback === true) {
        // Compare checksums to see if runtime actually changed
        if (currentVersion.checksum && runtimeInfo.checksum) {
          if (currentVersion.checksum !== runtimeInfo.checksum) {
            console.log(`[BACKEND] Runtime rollback required - checksum changed`);
            console.log(`[BACKEND] Current: ${currentVersion.checksum}`);
            console.log(`[BACKEND] Server: ${runtimeInfo.checksum}`);
            return true;
          } else {
            console.log(`[BACKEND] Runtime rollback skipped - same checksum (no actual changes)`);
            return false;
          }
        } else {
          // No checksums available - compare versions
          if (currentVersion.version !== runtimeInfo.version) {
            console.log(`[BACKEND] Runtime rollback: ${currentVersion.version} → ${runtimeInfo.version} (no checksum available)`);
            return true;
          } else {
            console.log(`[BACKEND] Runtime rollback skipped - same version (${runtimeInfo.version})`);
            return false;
          }
        }
      }

      // Compare release dates - update if server has newer release date
      const currentDate = new Date(currentVersion.releaseDate);
      const serverDate = new Date(runtimeInfo.releaseDate);
      return serverDate > currentDate;
    } catch {
      return true;
    }
  }

  /**
   * Check if scripts update is needed
   */
  async isScriptsUpdateNeeded(scriptsInfo: BackendVersionInfo | null): Promise<boolean> {
    if (!scriptsInfo) {
      return true; // First install
    }

    const scriptsPath = path.join(this.backendPath, 'scripts');

    // Check if main.pyc exists in scripts directory
    const mainPycPath = path.join(scriptsPath, 'main.pyc');
    if (!fs.existsSync(mainPycPath)) {
      return true;
    }

    // Check version file in scripts directory
    const versionPath = path.join(scriptsPath, 'scripts-version.json');
    if (!fs.existsSync(versionPath)) {
      return true;
    }

    try {
      const currentVersion = await fs.readJson(versionPath);

      // Check for rollback flag (forces update regardless of date)
      if (scriptsInfo.isRollback === true) {
        return true;
      }

      // Compare release dates - update if server has newer release date
      const currentDate = new Date(currentVersion.releaseDate);
      const serverDate = new Date(scriptsInfo.releaseDate);
      return serverDate > currentDate;
    } catch {
      return true;
    }
  }

  /**
   * Check if backend update is needed
   */
  async checkBackendUpdate(): Promise<{
    runtimeNeeded: boolean,
    scriptsNeeded: boolean,
    runtimeVersion?: string,
    scriptsVersion?: string,
    reason: string
  }> {
    try {
      // Check if we're in development mode
      const forceDownload = process.env.FORCE_DOWNLOAD === 'true';
      const isDev = !app.isPackaged && !forceDownload;

      // In development mode, check if backend is already ready
      if (isDev) {
        const backendReady = await this.isBackendReady();
        if (backendReady) {
          return {
            runtimeNeeded: false,
            scriptsNeeded: false,
            runtimeVersion: '1.0.0',
            scriptsVersion: '1.0.0',
            reason: 'Development mode - backend already ready'
          };
        }

        // If not ready, check what's missing
        const exePath = path.join(this.backendPath, 'One Whispr Backend.exe');
        const scriptsPath = path.join(this.backendPath, 'scripts');
        const hasExe = fs.existsSync(exePath);
        const hasScripts = fs.existsSync(scriptsPath);

        if (hasExe && !hasScripts) {
          // Only scripts are missing, check if we have Scripts.7z to extract
          const hasLocal7zFiles = this.checkForLocal7zFiles();
          if (hasLocal7zFiles.hasScriptsZip) {
            return {
              runtimeNeeded: false,
              scriptsNeeded: true,
              runtimeVersion: '1.0.0',
              scriptsVersion: '1.0.0',
              reason: 'Development mode - extracting scripts only'
            };
          }
        }
      }

      // Check runtime version
      let runtimeNeeded = false;
      let runtimeInfo: BackendVersionInfo | null = null;

      if (IS_MICROSOFT) {
        console.log('[BACKEND] Microsoft Store build - checking embedded runtime vs local runtime');

        // For Microsoft Store builds, check if embedded runtime is newer than local
        const versionCheck = await microsoftStoreHandler.instance.isEmbeddedRuntimeNewer();
        runtimeNeeded = versionCheck.isNewer;

        if (runtimeNeeded) {
          console.log(`[BACKEND] ${versionCheck.reason}`);
          // Create a mock runtime info for consistency
          runtimeInfo = {
            version: versionCheck.embeddedVersion || 'unknown',
            releaseDate: new Date().toISOString(),
            releaseNotes: 'Microsoft Store embedded runtime update',
            downloadUrl: 'embedded'
          };
        } else {
          console.log(`[BACKEND] ${versionCheck.reason}`);
        }
      } else {
        runtimeInfo = await this.fetchVersionInfo('runtime');
        runtimeNeeded = await this.isRuntimeUpdateNeeded(runtimeInfo);
      }

      // Check scripts version (always check for Microsoft Store - scripts can be updated via VPS)
      const scriptsInfo = await this.fetchVersionInfo('scripts');
      const scriptsNeeded = await this.isScriptsUpdateNeeded(scriptsInfo);

      let reason = 'All backend components up to date';
      if (runtimeNeeded && scriptsNeeded) {
        reason = 'First time installation - downloading base runtime and scripts';
      } else if (runtimeNeeded) {
        reason = 'Base runtime update available';
      } else if (scriptsNeeded) {
        reason = 'Backend scripts update available';
      }

      return {
        runtimeNeeded,
        scriptsNeeded,
        runtimeVersion: runtimeInfo?.version,
        scriptsVersion: scriptsInfo?.version,
        reason
      };
    } catch (error) {
      console.error('[BACKEND] Error checking updates:', error);
      return {
        runtimeNeeded: false,
        scriptsNeeded: false,
        reason: 'Error checking updates - will try to use existing backend'
      };
    }
  }

  /**
   * Check for local 7z files (from one.whispr-app build or Microsoft Store embedded) without extracting
   */
  checkForLocal7zFiles(): {
    hasRuntimeZip: boolean,
    hasScriptsZip: boolean,
    runtimeZipPath: string,
    scriptsZipPath: string
  } {
    let sourceBackendPath: string;
    let builtAppPath: string;

    if (IS_MICROSOFT) {
      // Microsoft Store: look for 7z files in copied MainApp (LocalState) after MainApp copy
      sourceBackendPath = microsoftStoreHandler.instance.getBackendPath();
      builtAppPath = path.dirname(path.dirname(sourceBackendPath)); // Get MainApp root from backend path
    } else {
      // Development: look in built app directory
      builtAppPath = path.join(process.cwd(), '..', 'one.whispr-app', '.release', 'win-unpacked');
      sourceBackendPath = path.join(builtAppPath, 'resources', 'backend');
    }

    const runtimeZipPath = path.join(sourceBackendPath, 'OneWhispr-Runtime-Base.7z');
    const scriptsZipPath = path.join(sourceBackendPath, 'OneWhispr-Scripts.7z');

    console.log('[VERSION] Checking local 7z files:');
    console.log('[VERSION] Built app path:', builtAppPath);
    console.log('[VERSION] Source backend path:', sourceBackendPath);
    console.log('[VERSION] Runtime zip path:', runtimeZipPath);
    console.log('[VERSION] Scripts zip path:', scriptsZipPath);
    
    const hasRuntimeZip = fs.existsSync(runtimeZipPath);
    const hasScriptsZip = IS_MICROSOFT ? false : fs.existsSync(scriptsZipPath); // Microsoft Store: scripts downloaded from VPS
    
    console.log('[VERSION] Runtime exists:', hasRuntimeZip);
    console.log('[VERSION] Scripts exists:', hasScriptsZip);
    
    if (IS_MICROSOFT) {
      console.log('[VERSION] Microsoft Store build - scripts.7z not included (downloaded from VPS)');
    }

    return {
      hasRuntimeZip,
      hasScriptsZip,
      runtimeZipPath,
      scriptsZipPath
    };
  }

  /**
   * Check if backend is ready to use
   */
  async isBackendReady(): Promise<boolean> {
    const exePath = path.join(this.backendPath, 'One Whispr Backend.exe');
    const scriptsPath = path.join(this.backendPath, 'scripts');
    const mainPycPath = path.join(scriptsPath, 'main.pyc');
    const whisprPath = path.join(scriptsPath, 'whispr');

    return fs.existsSync(exePath) &&
           fs.existsSync(mainPycPath) &&
           fs.existsSync(whisprPath);
  }
}
