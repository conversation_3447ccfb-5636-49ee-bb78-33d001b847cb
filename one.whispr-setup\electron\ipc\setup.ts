import { ipcMain, app } from 'electron';
import { closeLauncherWindow } from '../window';

/**
 * Setup IPC handlers for general setup operations
 */
export function setupSetupIpc(services: any): void {
  // Remove existing handlers first to prevent duplicates during hot reload
  ipcMain.removeHandler('launcher:get-env');
  ipcMain.removeHandler('launcher:exit');

  // Get environment variables
  ipcMain.handle('launcher:get-env', async () => {
    return {
      NODE_ENV: process.env.NODE_ENV,
      FORCE_DOWNLOAD: process.env.FORCE_DOWNLOAD,
      DOWNLOAD_PATH: process.env.DOWNLOAD_PATH
    };
  });

  // Exit launcher
  ipcMain.handle('launcher:exit', () => {
    console.log('[LAUNCHER] Exit requested via IPC');

    // Close the launcher window properly (sets isQuitting flag)
    closeLauncherWindow();

    // Force quit after a moment if normal quit doesn't work
    setTimeout(() => {
      app.quit();
      setTimeout(() => {
        process.exit(0);
      }, 1000);
    }, 500);

    return true;
  });

  console.log('[IPC] Setup handlers registered');
}
