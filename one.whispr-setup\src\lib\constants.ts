/**
 * Application constants
 */

// Determine if we're in development mode
// Use a more reliable method for Electron apps
export const IS_DEV = !process.env.NODE_ENV || process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'dev';

// Microsoft Store build detection - check if running from WindowsApps directory
export const IS_MICROSOFT = process.env.IS_MICROSOFT === 'true' ||
  (typeof process !== 'undefined' && process.execPath && process.execPath.includes('WindowsApps'));

// Base URL
export const SITE_URL = 'https://whispr.one';

// Update server URL (can be overridden by environment variable)
export const UPDATE_SERVER_URL = process.env.UPDATE_SERVER_URL || 'https://whispr.one/updates';

// Main app update URLs
export const MAIN_APP_UPDATES = {
  baseUrl: `${UPDATE_SERVER_URL}/main-app`,
  latestUrl: `${UPDATE_SERVER_URL}/main-app/latest`,
  versionUrl: `${UPDATE_SERVER_URL}/main-app/version.json`,
  manifestUrl: `${UPDATE_SERVER_URL}/main-app/latest/manifest.json`,
};

// Setup update URLs
export const SETUP_UPDATES = {
  baseUrl: `${UPDATE_SERVER_URL}/setup`,
  latestUrl: `${UPDATE_SERVER_URL}/setup/latest`,
  versionUrl: `${UPDATE_SERVER_URL}/setup/version.json`,
};

// Backend update URLs (NEW - Separated Build)
export const BACKEND_UPDATES = {
  runtime: {
    baseUrl: `${UPDATE_SERVER_URL}/backend-runtime`,
    latestUrl: `${UPDATE_SERVER_URL}/backend-runtime/latest`,
    versionUrl: `${UPDATE_SERVER_URL}/backend-runtime/runtime-version.json`,
    archiveUrl: `${UPDATE_SERVER_URL}/backend-runtime/latest/OneWhispr-Runtime-Base.7z`,
  },
  scripts: {
    baseUrl: `${UPDATE_SERVER_URL}/backend-scripts`,
    latestUrl: `${UPDATE_SERVER_URL}/backend-scripts/latest`,
    versionUrl: `${UPDATE_SERVER_URL}/backend-scripts/scripts-version.json`,
    archiveUrl: `${UPDATE_SERVER_URL}/backend-scripts/latest/OneWhispr-Scripts.7z`,
  },
};

export default {
  IS_DEV,
  IS_MICROSOFT,
  SITE_URL,
  UPDATE_SERVER_URL,
  MAIN_APP_UPDATES,
  SETUP_UPDATES,
  BACKEND_UPDATES,
};