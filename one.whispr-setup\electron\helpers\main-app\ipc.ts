import { ipcMain } from 'electron';

/**
 * Setup IPC handlers for main app download operations
 */
export function setupMainAppIpc(downloader: any): void {
  // Start download
  ipcMain.handle('download:start', async () => {
    return downloader.startDownload();
  });

  // Cancel download
  ipcMain.handle('download:cancel', () => {
    return downloader.cancelDownload();
  });

  // Check if download is needed
  ipcMain.handle('download:check-needed', async () => {
    return downloader.checkDownloadNeeded();
  });

  // Get manifest
  ipcMain.handle('download:get-manifest', () => {
    return downloader.getManifest();
  });
}
