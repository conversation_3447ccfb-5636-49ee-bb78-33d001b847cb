# OneWhispr Setup - Simplified Launcher & Updater

A lightweight Electron application that serves as an installer, updater, and launcher for the main OneWhispr application.

## Features

- **Efficient Updates**: Downloads only changed files instead of the entire application
- **Progress Tracking**: Real-time progress bars and status updates
- **Self-Updating**: Automatically updates itself using electron-updater
- **Simple UI**: Clean, minimal interface with OneWhispr branding
- **Error Handling**: Graceful error handling with user-friendly messages

## Architecture

The setup app works in three phases:

1. **Checking**: Checks if updates are needed by comparing versions and file checksums
2. **Downloading**: Downloads only the files that have changed (bandwidth efficient)
3. **Launching**: Launches the main OneWhispr application and closes itself

## Development

```bash
# Install dependencies
npm install

# Run in development mode (skips downloads, launches directly)
npm run dev

# Run in development mode with built UI (skips downloads, launches directly)
npm run dev-preview

# Run in development mode with downloads enabled (downloads to .dist/downloads)
npm run dev-download

# Build for production
npm run build

# Build installer
npm run build:files
```

## File Structure

```
one.whispr-setup/
├── electron/                 # Main process code
│   ├── main.ts              # Entry point with auto-updater
│   ├── window.ts            # Window management
│   ├── downloader.ts        # File download logic
│   ├── launcher.ts          # Main app launching
│   └── preload.ts           # Preload script for IPC
├── src/                     # Renderer process (React)
│   ├── components/          # React components
│   ├── hooks/               # React hooks (useSetup)
│   ├── lib/                 # Utilities and constants
│   └── types/               # TypeScript definitions
└── package.json             # Dependencies and build config
```

## How It Works

1. User downloads `OneWhisprSetup.exe` (small ~50-100MB)
2. Setup app checks for updates via manifest at `https://whispr.one/updates/main-app/latest/manifest.json`
3. Downloads only changed files to `%APPDATA%/OneWhispr/MainApp/`
4. Launches `One Whispr.exe` from the downloaded files
5. Setup app closes after confirming main app started

## Update System

- **Setup Updates**: Via electron-updater from `https://whispr.one/updates/setup/`
- **Main App Updates**: Via custom downloader from `https://whispr.one/updates/main-app/`
- **Bandwidth Savings**: ~90% reduction for existing users (only changed files downloaded)

## Configuration

Update URLs are configured in `src/lib/constants.ts`:

```typescript
export const MAIN_APP_UPDATES = {
  baseUrl: 'https://whispr.one/updates/main-app',
  manifestUrl: 'https://whispr.one/updates/main-app/latest/manifest.json',
  // ...
};
```

## Deployment

The app is deployed using GitHub Actions as described in `/.github/ELECTRON_DEPLOYMENT.md`. 