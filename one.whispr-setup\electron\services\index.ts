/**
 * Services registry - exports all services for easy importing
 */

// Download services
export { MainAppDownloader } from './download/mainAppDownloader';
export { BackendDownloader } from './download/backendDownloader';
export { FileDownloader } from './download/fileDownloader';
export { ComponentDownloaders } from './download/componentDownloaders';
export { ArchiveExtractor } from './download/archiveExtractor';
export { LocalExtractor } from './download/localExtractor';

// Version services
export { VersionChecker } from './version/versionChecker';
export { VersionManager } from './version/versionManager';

// Launcher services
export { AppLauncher } from './launcher/appLauncher';
export { LaunchPathResolver } from './launcher/launchPathResolver';
export { ProcessChecker } from './launcher/processChecker';
export { LaunchReadinessChecker } from './launcher/launchReadinessChecker';
export { MainAppLauncher } from './launcher/mainAppLauncher';

// Microsoft Store services
export { MicrosoftStoreHandler } from './microsoft/storeHandler';

// Singleton instances for backward compatibility
import { MainAppDownloader as MainAppDownloaderClass } from './download/mainAppDownloader';
import { BackendDownloader as BackendDownloaderClass } from './download/backendDownloader';
import { MicrosoftStoreHandler as MicrosoftStoreHandlerClass } from './microsoft/storeHandler';
import { AppLauncher as AppLauncherClass } from './launcher/appLauncher';

export const downloader = new MainAppDownloaderClass();
export const backendDownloader = new BackendDownloaderClass();
export const microsoftStoreHandler = new MicrosoftStoreHandlerClass();
export const launcher = new AppLauncherClass(downloader, backendDownloader, microsoftStoreHandler);
