import { ipcMain } from 'electron';

/**
 * Setup IPC handlers for launcher operations
 */
export function setupLauncherIpc(services: any): void {
  // Remove existing handlers first to prevent duplicates during hot reload
  ipcMain.removeHandler('launcher:check-ready');
  ipcMain.removeHandler('launcher:launch-main-app');
  ipcMain.removeHandler('launcher:microsoft-setup');

  // Check if everything is ready to launch
  ipcMain.handle('launcher:check-ready', async () => {
    return services.launcher?.checkLaunchReady();
  });

  // Launch main app
  ipcMain.handle('launcher:launch-main-app', async () => {
    return services.launcher?.launchMainApp();
  });

  // Handle Microsoft Store setup
  ipcMain.handle('launcher:microsoft-setup', async () => {
    return services.launcher?.handleMicrosoftStoreSetup();
  });

  console.log('[IPC] Launcher handlers registered');
}
