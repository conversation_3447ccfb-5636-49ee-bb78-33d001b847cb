/**
 * Centralized IPC registry - sets up all IPC handlers
 */

import { setupSetupIpc } from './setup';
import { setupDownloadIpc } from './download';
import { setupLauncherIpc } from './launcher';

/**
 * Initialize all IPC handlers
 */
export function initializeIpc(services: any): void {
  console.log('[IPC] Initializing all IPC handlers...');
  
  // Setup all IPC handlers
  setupSetupIpc(services);
  setupDownloadIpc(services);
  setupLauncherIpc(services);
  
  console.log('[IPC] All IPC handlers initialized successfully');
}
