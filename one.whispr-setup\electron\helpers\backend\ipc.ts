import { ipcMain } from 'electron';

/**
 * Setup IPC handlers for backend download operations
 */
export function setupBackendIpc(downloader: any): void {
  // Check if backend update is needed
  ipcMain.handle('backend:check-update', async () => {
    return downloader.checkBackendUpdate();
  });

  // Start backend download/update
  ipcMain.handle('backend:download', async () => {
    return downloader.downloadBackend();
  });

  // Cancel backend download
  ipcMain.handle('backend:cancel', () => {
    return downloader.cancelDownload();
  });
}
