# -*- mode: python ; coding: utf-8 -*-

# Separated build spec for One.Whispr Backend
#
# This creates a base runtime with _internal folder structure
# main.py and whispr folder will be kept separate for quick updates
#
# UPX compression is DISABLED for separated builds because:
# - 7zip ultra compression provides better overall compression
# - Avoids potential runtime issues with compressed DLLs in _internal
# - Separated builds use 7zip for final compression instead

import os
import silero_vad
from PyInstaller.building.build_main import Analysis, PYZ, EXE, COLLECT

block_cipher = None

# Define the project root and paths
project_root = os.path.dirname(os.path.abspath(SPEC))
main_script = os.path.join(project_root, 'main.py')
icon_path = os.path.join(project_root, '..', 'src', 'assets', 'icon.ico')
silero_vad_data_path = os.path.join(os.path.dirname(silero_vad.__file__), 'data')

# Analysis with all dependencies
a = Analysis(
    [main_script],
    pathex=[project_root],
    binaries=[],
    datas=[(silero_vad_data_path, 'silero_vad/data'),],
    hiddenimports=[
        # Essential core dependencies only
        'silero_vad',
        'numpy',
        'torch',
        'torch._C',  # Critical: PyTorch C++ extensions
        'torch.nn.functional',
        'torch.autograd',
        'torch.cuda',
        'transformers',
        'sounddevice',
        'websockets',
        'asyncio',

        # DO NOT include whispr modules here - they should be updatable
        # whispr modules will be loaded from the updatable bytecode package
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Create Python archive with bytecode optimization
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher, optimize=2)

# Create folder structure (not onefile) for _internal approach
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,  # This creates the _internal folder structure
    name='One Whispr Backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # Disable UPX for separated builds to avoid _internal issues
    console=True,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path
)

# Collect all files for folder structure with _internal 
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,     # Strip debug symbols to reduce size
    upx=False,      # Disable UPX entirely for separated builds
    upx_exclude=[], # No exclusions needed since UPX is disabled
    name='One Whispr Backend'
)
